/* Blog Post Page Styles */
.blogPostPage {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #1e293b 75%, #0f172a 100%);
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Hero Container - Left aligned */
.heroContainer {
  max-width: 1200px;
  margin: 0;
  padding: 0 20px;
  width: 100%;
}

/* Hero Section */
.heroSection {
  position: relative;
  height: 60vh;
  min-height: 500px;
  display: flex;
  align-items: center;
  overflow: hidden;
  justify-content: flex-start;
}

.heroBackground {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.heroBackgroundNoImage {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(147, 51, 234, 0.2));
}

.heroImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.heroOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.8), rgba(30, 41, 59, 0.6));
}

.heroContent {
  position: relative;
  z-index: 2;
  color: #ffffff;
  max-width: 800px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

/* Breadcrumb */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 24px;
  font-size: 14px;
  color: #94a3b8;
  justify-content: flex-start;
  width: 100%;
}

.breadcrumb a {
  color: #3b82f6;
  text-decoration: none;
  transition: color 0.3s ease;
}

.breadcrumb a:hover {
  color: #60a5fa;
}

.breadcrumb span:not(.category):not(.readTime):not(.featuredBadge) {
  color: #64748b;
}

/* Post Meta */
.postMeta {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  flex-wrap: wrap;
  justify-content: flex-start;
  width: 100%;
}

.category {
  background: rgba(59, 130, 246, 0.9);
  color: #ffffff;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

.readTime {
  display: flex;
  align-items: center;
  gap: 6px;
  background: rgba(0, 0, 0, 0.6);
  color: #ffffff;
  padding: 8px 12px;
  border-radius: 15px;
  font-size: 12px;
  backdrop-filter: blur(10px);
}

.featuredBadge {
  display: flex;
  align-items: center;
  gap: 6px;
  background: linear-gradient(135deg, #f59e0b, #f97316);
  color: #ffffff;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

/* Post Title */
.postTitle {
  font-size: clamp(2rem, 4vw, 3.5rem);
  font-weight: 800;
  line-height: 1.2;
  margin-bottom: 32px;
  background: linear-gradient(135deg, #ffffff, #e2e8f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-align: left;
  width: 100%;
}

/* Author Section */
.authorSection {
  display: flex;
  align-items: center;
  gap: 16px;
  justify-content: flex-start;
  width: 100%;
}

.authorAvatar {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 20px;
}

.authorInfo {
  display: flex;
  flex-direction: column;
}

.authorName {
  color: #ffffff;
  font-weight: 600;
  font-size: 16px;
}

.postDate {
  color: #94a3b8;
  font-size: 14px;
}

/* Content Section */
.contentSection {
  padding: 80px 0;
  position: relative;
}

.contentWrapper {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 60px;
  align-items: start;
}

/* Post Content */
.postContent {
  background: rgba(30, 41, 59, 0.8);
  border-radius: 24px;
  padding: 40px;
  border: 1px solid rgba(59, 130, 246, 0.1);
  backdrop-filter: blur(10px);
}

.content {
  color: #e2e8f0;
  line-height: 1.8;
  font-size: 16px;
}

.content h2 {
  color: #ffffff;
  font-size: 1.8rem;
  font-weight: 700;
  margin: 40px 0 20px 0;
  padding-bottom: 12px;
  border-bottom: 2px solid rgba(59, 130, 246, 0.2);
}

.content h2:first-child {
  margin-top: 0;
}

.content h3 {
  color: #3b82f6;
  font-size: 1.4rem;
  font-weight: 600;
  margin: 32px 0 16px 0;
}

.content p {
  margin-bottom: 20px;
}

.content ul,
.content ol {
  margin: 20px 0;
  padding-left: 24px;
}

.content li {
  margin-bottom: 8px;
  color: #cbd5e1;
}

.content strong {
  color: #ffffff;
  font-weight: 600;
}

.content a {
  color: #3b82f6;
  text-decoration: none;
  border-bottom: 1px solid rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.content a:hover {
  color: #60a5fa;
  border-bottom-color: #60a5fa;
}

/* Post Tags */
.postTags {
  margin-top: 40px;
  padding-top: 32px;
  border-top: 1px solid rgba(59, 130, 246, 0.2);
}

.postTags h4 {
  color: #ffffff;
  font-size: 1.1rem;
  margin-bottom: 16px;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.tag {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  border: 1px solid rgba(59, 130, 246, 0.2);
  transition: all 0.3s ease;
}

.tag:hover {
  background: rgba(59, 130, 246, 0.2);
  transform: translateY(-2px);
}

/* Sidebar */
.sidebar {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.sidebarCard {
  background: rgba(30, 41, 59, 0.8);
  border-radius: 20px;
  padding: 32px;
  border: 1px solid rgba(59, 130, 246, 0.1);
  backdrop-filter: blur(10px);
}

.sidebarCard h3 {
  color: #ffffff;
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 24px;
  padding-bottom: 12px;
  border-bottom: 2px solid rgba(59, 130, 246, 0.2);
}

/* Author Card */
.authorCard {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.authorCard .authorAvatar {
  width: 60px;
  height: 60px;
  font-size: 24px;
  flex-shrink: 0;
}

.authorDetails h4 {
  color: #ffffff;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 8px;
}

.authorDetails p {
  color: #94a3b8;
  line-height: 1.6;
  font-size: 14px;
}

/* Related Posts */
.relatedPosts {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.relatedPost {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: rgba(51, 65, 85, 0.5);
  border-radius: 12px;
  text-decoration: none;
  transition: all 0.3s ease;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.relatedPost:hover {
  background: rgba(59, 130, 246, 0.1);
  transform: translateY(-2px);
  border-color: rgba(59, 130, 246, 0.3);
}

.relatedPost img {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 8px;
  flex-shrink: 0;
}

.relatedPostPlaceholder {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
  border-radius: 8px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholderIcon {
  color: rgba(59, 130, 246, 0.6);
}

.relatedContent {
  flex: 1;
}

.relatedContent h5 {
  color: #ffffff;
  font-size: 14px;
  font-weight: 600;
  line-height: 1.4;
  margin-bottom: 6px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.relatedDate {
  color: #64748b;
  font-size: 12px;
}

.relatedPlaceholder {
  color: #64748b;
  font-style: italic;
  text-align: center;
  padding: 20px;
  font-size: 14px;
}

/* Navigation Section */
.navigationSection {
  margin-top: 60px;
  padding-top: 40px;
  border-top: 1px solid rgba(59, 130, 246, 0.2);
  text-align: center;
}

.backToBlogs {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: #ffffff;
  text-decoration: none;
  padding: 16px 32px;
  border-radius: 25px;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.backToBlogs::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.backToBlogs:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 35px rgba(59, 130, 246, 0.4);
}

.backToBlogs:hover::before {
  left: 100%;
}

.backToBlogs i {
  transition: transform 0.3s ease;
}

.backToBlogs:hover i {
  transform: translateX(-4px);
}

/* Not Found */
.notFound {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #1e293b 75%, #0f172a 100%);
  text-align: center;
  color: #ffffff;
}

.notFound h1 {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 16px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.notFound p {
  font-size: 1.2rem;
  color: #94a3b8;
  margin-bottom: 32px;
}

.backButton {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: #ffffff;
  text-decoration: none;
  padding: 12px 24px;
  border-radius: 20px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.backButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.4);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .contentWrapper {
    grid-template-columns: 1fr;
    gap: 40px;
  }
  
  .sidebar {
    order: -1;
  }
}

@media (max-width: 768px) {
  .heroSection {
    height: 50vh;
    min-height: 400px;
  }
  
  .container {
    padding: 0 16px;
  }
  
  .postTitle {
    font-size: 2rem;
  }
  
  .postContent {
    padding: 24px;
  }
  
  .content {
    font-size: 15px;
  }
  
  .content h2 {
    font-size: 1.5rem;
  }
  
  .content h3 {
    font-size: 1.2rem;
  }
  
  .sidebarCard {
    padding: 24px;
  }
  
  .authorSection {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .postMeta {
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .heroSection {
    height: 40vh;
    min-height: 300px;
  }
  
  .postTitle {
    font-size: 1.8rem;
  }
  
  .postContent {
    padding: 20px;
  }
  
  .breadcrumb {
    font-size: 12px;
  }
  
  .authorCard {
    flex-direction: column;
    text-align: center;
  }
  
  .relatedPost {
    flex-direction: column;
  }
  
  .relatedPost img {
    width: 100%;
    height: 120px;
  }
}